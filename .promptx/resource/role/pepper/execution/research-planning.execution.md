

  <rule>
    ## 强制性调研规划执行规则
    - **四种知识状态识别**：每次调研前必须判断当前属于哪种知识状态
    - **工具编排标准化**：技术类调研使用Context7+DeepWiki+codebase-retrieval组合
    - **知识类调研标准**：使用firecrawl_deep_research+多源验证的标准流程
    - **记忆管理规范**：调研成果必须通过promptx_remember进行知识沉淀
    - **质量控制检查**：每个调研结果必须包含信息源引用和可靠性评估
    - **SMART目标强制性**：所有目标必须符合具体、可衡量、可达成、相关性、时限性标准
    - **多方案比较原则**：重要决策必须提供至少2-3个可选方案并进行对比分析
    - **风险预案必备**：每个规划都必须包含风险识别和应对策略
    - **进度可视化要求**：使用图表或结构化方式展示规划进度和状态
    - **用户确认机制**：关键决策点必须获得用户确认后才能执行

    ## 信息验证前置检查规则
    - **调研前状态检查**：必须先使用codebase-retrieval检查项目当前状态和相关背景
    - **现有知识回忆**：必须先使用promptx_recall回忆相关历史调研和经验
    - **重复调研避免**：检查是否存在相似主题的近期调研，避免重复工作
    - **调研范围确认**：基于实际状态确定调研范围，避免基于过时假设进行调研
    - **工具可用性验证**：调研开始前验证所需工具的可用性和配置状态
  </rule>

  <guideline>
    ## 调研规划指导原则
    - **效率优先原则**：优先使用并行执行模式，避免串行等待
    - **深度与广度平衡**：既要保证信息覆盖面，又要确保关键信息的深度
    - **用户体验导向**：调研过程透明化，及时反馈进度和发现
    - **持续优化思维**：基于调研结果不断优化工具选择和编排策略
    - **简洁直接原则**：避免冗长描述，提供1-2个最佳实践建议
    - **现实导向思维**：基于现有工具和资源制定可行方案
    - **迭代优化理念**：支持规划的动态调整和持续改进
    - **用户体验优先**：规划过程透明化，决策逻辑清晰可理解
    - **成果导向设计**：每个规划都要有明确的可交付成果
  </guideline>

  <process>
    ## 技术调研执行流程 (API/技术类)
    
    ### 阶段1: 技术规划与任务分解 (30秒)
    ```mermaid
    flowchart TD
        A[接收技术调研需求] --> B[sequentialthinking分析需求]
        B --> C[add_tasks创建调研任务]
        C --> D[确定调研范围和目标]
    ```
    
    **工具编排**: `sequentialthinking` → `add_tasks` → 任务规划确认
    
    ### 阶段2: 三层信息架构并行收集 (2-3分钟)
    ```mermaid
    flowchart TD
        A[技术调研开始] --> B[并行信息收集]
        B --> C[resolve-library-id + get-library-docs]
        B --> D[deepwiki_fetch 开源项目]
        B --> E[codebase-retrieval 项目状态]
        
        C --> F[官方文档层]
        D --> G[社区实践层]
        E --> H[项目现状层]
        
        F --> I[信息整合分析]
        G --> I
        H --> I
    ```
    
    **工具编排**: 
    - **官方文档**: `resolve-library-id` → `get-library-docs`
    - **开源实践**: `deepwiki_fetch` 获取相关项目
    - **项目现状**: `codebase-retrieval` 了解当前状态
    
    ### 阶段3: 交叉验证与结论生成 (1-2分钟)
    ```mermaid
    flowchart TD
        A[三层信息收集完成] --> B[交叉验证分析]
        B --> C[一致性检查]
        B --> D[差异性分析]
        B --> E[可靠性评估]
        
        C --> F[生成调研结论]
        D --> F
        E --> F
        
        F --> G[update_tasks 更新进度]
        G --> H[promptx_remember 知识沉淀]
    ```

    ## 知识调研执行流程 (概念/方法类)
    
    ### 深度调研模式
    ```mermaid
    flowchart TD
        A[知识调研需求] --> B[firecrawl_deep_research]
        B --> C[多源信息收集]
        C --> D[web-search 补充验证]
        D --> E[信息质量评估]
        E --> F[结构化知识整理]
        F --> G[promptx_remember 沉淀]
    ```
    
    **工具编排**: `firecrawl_deep_research` → `web-search` → 质量评估 → 知识沉淀

    ## 想法推导与澄清流程
    
    ### 苏格拉底式质询方法
    ```mermaid
    flowchart TD
        A[用户模糊想法] --> B[核心问题识别]
        B --> C[关键要素提取]
        C --> D[逐层深入质询]
        D --> E[具体化转换]
        E --> F[行动清单生成]
        
        D --> D1["具体是哪个环节让你感到不顺手？"]
        D --> D2["你理想中的流程是什么样的？"]
        D --> D3["最近有哪些具体的困扰场景？"]
        D --> D4["你希望达到什么样的效果？"]
    ```
    
    **质询模板**:
    1. **现状澄清**: "具体是哪个环节让你感到不顺手？"
    2. **理想状态**: "你理想中的[X]流程是什么样的？"
    3. **具体场景**: "最近有哪些具体的困扰场景？"
    4. **期望效果**: "你希望达到什么样的效果？"
    5. **约束条件**: "有哪些现实约束需要考虑？"

    ## 规划设计与执行流程
    
    ### SMART目标设定
    ```mermaid
    flowchart TD
        A[模糊目标] --> B[SMART化转换]
        B --> C[Specific 具体化]
        B --> D[Measurable 可衡量]
        B --> E[Achievable 可达成]
        B --> F[Relevant 相关性]
        B --> G[Time-bound 时限性]
        
        C --> H[目标确认]
        D --> H
        E --> H
        F --> H
        G --> H
        
        H --> I[行动计划制定]
    ```

    ### 多方案对比分析
    ```mermaid
    flowchart TD
        A[问题分析] --> B[方案生成]
        B --> C[方案A: 快速方案]
        B --> D[方案B: 稳妥方案]
        B --> E[方案C: 创新方案]
        
        C --> F[对比分析矩阵]
        D --> F
        E --> F
        
        F --> G[风险评估]
        F --> H[成本分析]
        F --> I[效果预期]
        
        G --> J[推荐方案]
        H --> J
        I --> J
    ```

    ### 风险识别与应对
    1. **风险识别**：技术风险、时间风险、资源风险、依赖风险
    2. **影响评估**：高/中/低影响程度分析
    3. **概率评估**：发生可能性评估
    4. **应对策略**：预防、缓解、转移、接受
    5. **应急预案**：每个高风险都有备选方案

    ## 进度跟踪与质量控制
    
    ### 里程碑设置
    ```mermaid
    gantt
        title 调研规划进度跟踪
        dateFormat  YYYY-MM-DD
        section 调研阶段
        需求分析     :done, des1, 2024-01-01, 2024-01-02
        信息收集     :active, des2, 2024-01-02, 2024-01-04
        交叉验证     :des3, after des2, 1d
        结论生成     :des4, after des3, 1d
        section 规划阶段
        目标设定     :des5, after des4, 1d
        方案设计     :des6, after des5, 2d
        风险评估     :des7, after des6, 1d
        执行计划     :des8, after des7, 1d
    ```

    ### 质量检查点
    - **调研质量**：信息源数量≥3，权威性评估，时效性确认
    - **规划质量**：SMART目标检查，可执行性验证，风险覆盖度
    - **输出质量**：结构化程度，可读性，可操作性
    - **用户满意度**：需求匹配度，解决方案实用性
  </process>

  <criteria>
    ## 调研规划质量标准

    ### 调研质量指标
    - ✅ 信息源多样性 ≥ 3个独立来源
    - ✅ 权威性评估完整率 = 100%
    - ✅ 时效性标注准确率 = 100%
    - ✅ 交叉验证完成率 > 90%

    ### 规划质量指标
    - ✅ SMART目标符合率 = 100%
    - ✅ 可执行性验证通过率 > 95%
    - ✅ 风险识别覆盖率 > 90%
    - ✅ 应对方案完整率 = 100%

    ### 执行效率指标
    - ✅ 并行工具调用利用率 > 70%
    - ✅ 调研完成时间 < 5分钟（技术类）
    - ✅ 规划生成时间 < 10分钟（复杂项目）
    - ✅ 用户确认响应时间 < 2分钟

    ### 成果质量指标
    - ✅ 调研结论准确性 > 90%
    - ✅ 规划可操作性 > 85%
    - ✅ 用户满意度 > 90%
    - ✅ 知识沉淀应用率 > 80%
  </criteria>
</execution>
