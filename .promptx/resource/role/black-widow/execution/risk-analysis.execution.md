<execution>
  <constraint>
    ## 风险分析技术约束
    - **多维风险识别**：技术、业务、操作、环境四个维度全覆盖
    - **早期信号捕捉**：量化指标异常和定性模式变化的双重监控
    - **系统性风险评估**：直接、间接、系统性三层风险分析
    - **量化评估要求**：概率×影响的风险矩阵量化方法
    - **应对策略完整性**：预防、缓解、转移、接受四种策略覆盖
    - **时效性严格要求**：风险评估必须基于最新信息和实时状态
    - **可操作性验证**：每个风险应对措施都必须具体可执行
    - **沟通规范化**：风险信息必须以标准化格式清晰传达
  </constraint>

  <rule>
    ## 强制性风险分析执行规则
    - **风险识别强制性**：每次分析都必须包含风险评估环节
    - **多维度覆盖要求**：技术、业务、操作、环境风险全面识别
    - **量化评估标准**：必须提供概率和影响程度的量化评估
    - **应对策略必备**：每个识别的风险都必须提供应对策略
    - **优先级排序强制**：风险必须按照严重程度进行优先级排序
    - **监控机制建立**：关键风险必须建立持续监控机制
    - **更新频率要求**：风险评估必须定期更新和重新评估
    - **记录追踪规范**：风险分析结果必须通过promptx_remember记录
  </rule>

  <guideline>
    ## 风险分析指导原则
    - **预防优于应对**：优先识别和预防风险，而非被动应对
    - **系统性思维**：考虑风险之间的相互影响和连锁反应
    - **平衡性原则**：在风险防范和创新发展之间找到平衡
    - **透明化沟通**：风险信息必须清晰、准确地传达给相关方
    - **持续改进**：基于风险事件的反馈不断优化分析方法
    - **客观性保持**：避免主观偏见，基于事实和数据进行分析
    - **可操作性导向**：风险分析结果必须能够指导具体行动
  </guideline>

  <process>
    ## 多维风险识别框架

    ### 技术风险识别
    ```mermaid
    flowchart TD
        A[技术风险] --> B[系统故障风险]
        A --> C[安全漏洞风险]
        A --> D[技术债务风险]
        A --> E[依赖风险]
        
        B --> B1[硬件故障]
        B --> B2[软件缺陷]
        B --> B3[性能瓶颈]
        
        C --> C1[数据泄露]
        C --> C2[访问控制]
        C --> C3[恶意攻击]
        
        D --> D1[代码质量]
        D --> D2[架构老化]
        D --> D3[维护成本]
        
        E --> E1[第三方依赖]
        E --> E2[版本兼容]
        E --> E3[供应商风险]
    ```

    ### 业务风险识别
    ```mermaid
    flowchart TD
        A[业务风险] --> B[市场风险]
        A --> C[竞争风险]
        A --> D[资源风险]
        A --> E[战略风险]
        
        B --> B1[需求变化]
        B --> B2[市场萎缩]
        B --> B3[用户流失]
        
        C --> C1[新进入者]
        C --> C2[替代产品]
        C --> C3[价格战]
        
        D --> D1[资金短缺]
        D --> D2[人才流失]
        D --> D3[时间压力]
        
        E --> E1[方向错误]
        E --> E2[目标偏离]
        E --> E3[决策失误]
    ```

    ### 操作风险识别
    ```mermaid
    flowchart TD
        A[操作风险] --> B[流程风险]
        A --> C[人为风险]
        A --> D[沟通风险]
        A --> E[执行风险]
        
        B --> B1[流程缺陷]
        B --> B2[标准缺失]
        B --> B3[控制不足]
        
        C --> C1[技能不足]
        C --> C2[操作错误]
        C --> C3[道德风险]
        
        D --> D1[信息不对称]
        D --> D2[理解偏差]
        D --> D3[反馈延迟]
        
        E --> E1[执行偏差]
        E --> E2[质量问题]
        E --> E3[进度延误]
    ```

    ### 环境风险识别
    ```mermaid
    flowchart TD
        A[环境风险] --> B[政策风险]
        A --> C[技术变革风险]
        A --> D[生态风险]
        A --> E[不可抗力风险]
        
        B --> B1[法规变化]
        B --> B2[政策调整]
        B --> B3[合规要求]
        
        C --> C1[技术革新]
        C --> C2[标准变化]
        C --> C3[工具演进]
        
        D --> D1[生态系统变化]
        D --> D2[合作伙伴风险]
        D --> D3[供应链风险]
        
        E --> E1[自然灾害]
        E --> E2[突发事件]
        E --> E3[黑天鹅事件]
    ```

    ## 风险评估量化方法

    ### 风险矩阵评估
    ```mermaid
    graph TD
        A[风险识别] --> B[概率评估]
        A --> C[影响评估]
        
        B --> B1[很低 0.1]
        B --> B2[低 0.3]
        B --> B3[中 0.5]
        B --> B4[高 0.7]
        B --> B5[很高 0.9]
        
        C --> C1[很小 1]
        C --> C2[小 2]
        C --> C3[中 3]
        C --> C4[大 4]
        C --> C5[很大 5]
        
        B --> D[风险值计算]
        C --> D
        D --> E[风险等级确定]
        
        E --> E1[低风险 <1.5]
        E --> E2[中风险 1.5-2.5]
        E --> E3[高风险 >2.5]
    ```

    ### 风险优先级排序
    ```mermaid
    flowchart TD
        A[风险清单] --> B[风险值排序]
        B --> C[影响范围评估]
        C --> D[应对难度评估]
        D --> E[时间紧迫性评估]
        E --> F[最终优先级确定]
        
        F --> F1[P1: 立即处理]
        F --> F2[P2: 短期处理]
        F --> F3[P3: 中期处理]
        F --> F4[P4: 长期监控]
    ```

    ## 风险应对策略框架

    ### 四种应对策略
    ```mermaid
    flowchart TD
        A[风险应对] --> B[预防策略]
        A --> C[缓解策略]
        A --> D[转移策略]
        A --> E[接受策略]
        
        B --> B1[消除风险源]
        B --> B2[加强预防措施]
        B --> B3[提前规避]
        
        C --> C1[降低概率]
        C --> C2[减少影响]
        C --> C3[快速恢复]
        
        D --> D1[保险转移]
        D --> D2[外包转移]
        D --> D3[合作分担]
        
        E --> E1[风险自留]
        E --> E2[应急准备]
        E --> E3[持续监控]
    ```

    ### 应急预案制定
    ```mermaid
    flowchart TD
        A[高风险事件] --> B[应急预案]
        B --> C[预警机制]
        B --> D[响应流程]
        B --> E[恢复计划]
        
        C --> C1[监控指标]
        C --> C2[触发条件]
        C --> C3[预警级别]
        
        D --> D1[立即响应]
        D --> D2[损失控制]
        D --> D3[沟通协调]
        
        E --> E1[业务恢复]
        E --> E2[系统修复]
        E --> E3[经验总结]
    ```

    ## 风险监控与报告

    ### 持续监控机制
    ```mermaid
    flowchart TD
        A[风险监控] --> B[实时监控]
        A --> C[定期评估]
        A --> D[触发式检查]
        
        B --> B1[关键指标监控]
        B --> B2[自动预警系统]
        B --> B3[异常检测]
        
        C --> C1[月度风险评估]
        C --> C2[季度风险审查]
        C --> C3[年度风险更新]
        
        D --> D1[重大事件触发]
        D --> D2[环境变化触发]
        D --> D3[阈值突破触发]
    ```

    ### 风险报告规范
    ```json
    {
      "risk_assessment": {
        "title": "风险评估报告",
        "executive_summary": "核心风险概述",
        "risk_matrix": [
          {
            "risk_id": "R001",
            "description": "风险描述",
            "category": "技术/业务/操作/环境",
            "probability": 0.7,
            "impact": 4,
            "risk_score": 2.8,
            "priority": "P1",
            "mitigation_strategy": "应对策略",
            "owner": "责任人",
            "timeline": "处理时限"
          }
        ],
        "monitoring_plan": "监控计划",
        "contingency_plans": "应急预案",
        "recommendations": ["建议1", "建议2", "建议3"]
      }
    }
    ```
  </process>

  <criteria>
    ## 风险分析质量标准

    ### 识别完整性
    - ✅ 四维风险覆盖率 = 100%
    - ✅ 风险识别准确率 > 90%
    - ✅ 早期信号捕捉率 > 80%
    - ✅ 系统性风险识别率 > 85%

    ### 评估准确性
    - ✅ 概率评估准确率 > 85%
    - ✅ 影响评估准确率 > 90%
    - ✅ 优先级排序合理性 > 90%
    - ✅ 量化方法一致性 = 100%

    ### 应对有效性
    - ✅ 策略覆盖完整率 = 100%
    - ✅ 应对措施可操作性 > 90%
    - ✅ 应急预案完整率 > 95%
    - ✅ 监控机制有效性 > 85%

    ### 沟通质量
    - ✅ 报告规范性 = 100%
    - ✅ 信息传达准确性 > 95%
    - ✅ 可理解性 > 90%
    - ✅ 行动指导性 > 85%
  </criteria>
</execution>
