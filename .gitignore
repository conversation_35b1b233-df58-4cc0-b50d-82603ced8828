# ==========================================
# Git 忽略规则 - 数字花园私有备份优化
# 原则：最小化忽略，只排除大文件和可重建文件
# 适用场景：GitHub私有仓库作为备份和版本控制
# ==========================================

# === 系统生成文件 ===
.DS_Store
Thumbs.db
desktop.ini
*.tmp
*.temp

# === 开发工具缓存（可重建）===
__pycache__/
*.pyc
*.pyo
*.pyd
.Python
build/
dist/
*.egg-info/
*.dist-info/
.pytest_cache/
node_modules/
.next/
.nuxt/

# === 虚拟环境（可重建）===
venv/
.venv/
env/
.env
ENV/
.python-version
.node-version

# === 编辑器临时文件 ===
*.swp
*.swo
*~
.#*
\#*\#
.vscode/settings.json

# === 大型媒体文件（>50MB，GitHub限制）===
# 保留小型视频用于演示和教学
*.mp4
*.mov
*.avi
*.mkv
*.wmv
*.flv
*.webm
*.m4v

# === 大型音频文件（>10MB）===
*.wav
*.m4a
*.m4b
*.m4p
*.flac

# === 大型压缩文件（>25MB）===
*.zip
*.rar
*.7z
*.tar.gz
*.tar.bz2
*.tar.xz
*.iso
*.dmg

# === 大型文档文件（>10MB）===
# 保留重要的小型PDF文档
*.doc
*.docx
*.ppt
*.pptx
*.xls
*.xlsx

# === 日志和缓存文件 ===
*.log
*.cache
.npm/
.yarn/

# === 备份文件 ===
*.bak
*.backup
*.old
*.orig

# === Obsidian设备特定配置（避免多设备冲突）===
.obsidian/workspace*.json
.obsidian/app.json
.obsidian/appearance.json
.obsidian/hotkeys.json
.obsidian/graph.json
.obsidian/page-preview.json

# === 同步冲突文件 ===
*.sync-conflict-*

# === Syncthing 元数据 ===
.stfolder/
.stversions/

# === AI工具索引（可重建）===
.copilot-index/
.cursor-index/
.augment-index/
copilot-index*.jsonx
*.index

# === 项目特定大文件目录 ===
11-Video-Factory/experimental/
11-Video-Factory/backup/
Asset/fonts/
Library/cherry-studio/

# === 备份和临时目录 ===
.promptx/backup/
Backup/*/
Temp/telegram/
Temp/downloads/

# === 第三方服务缓存 ===
.readwise-cache/
.notion-cache/

# ==========================================
# ✅ 保留的有价值文件类型（数字花园核心）
# ✅ *.md - Markdown文档（知识库核心）
# ✅ *.png, *.jpg, *.jpeg, *.gif, *.svg - 图片资源
# ✅ *.json - 配置文件（除了索引文件）
# ✅ *.txt, *.csv - 文本和数据文件
# ✅ *.py, *.js, *.ts - 代码文件（项目开发）
# ✅ 小型*.pdf - 重要文档（<10MB）
# ✅ .obsidian/plugins/ - 插件配置
# ✅ .obsidian/snippets/ - CSS片段
# ==========================================
